export interface ChatMessage {
  from: "user" | "avatar";
  message: string;
  timestamp: number;
  type?: "general" | "question" | "end" | null;
  category?: "communication" | "technical" | "behavioral" | null;
  // Metrics for user answers
  responseTime?: number; // Time in seconds from question finished to user started speaking
  answerDuration?: number; // Time in seconds for the user's answer
  fillerWordCount?: number;
}

const INTERVIEWS_KEY = "interviews";

export function saveInterview(id: string, chat: ChatMessage[]) {
  if (typeof window === "undefined") return;
  
  const interviews = JSON.parse(localStorage.getItem(INTERVIEWS_KEY) || "{}");
  const interviewIds = Object.keys(interviews);
  
  // If interview with this ID already exists, update it and maintain order
  if (interviewIds.includes(id)) {
    interviews[id] = chat;
  } else {
    // Add new interview
    interviews[id] = chat;
    interviewIds.push(id);
    
    // If we have more than 3 interviews, remove the oldest one (first in the array)
    if (interviewIds.length > 3) {
      const oldestId = interviewIds[0];
      delete interviews[oldestId];
    }
  }
  
  localStorage.setItem(INTERVIEWS_KEY, JSON.stringify(interviews));
}

export function getInterview(id: string): ChatMessage[] | null {
  if (typeof window === "undefined") return null;
  const interviews = JSON.parse(localStorage.getItem(INTERVIEWS_KEY) || "{}");
  return interviews[id] || null;
}

export function getInterviewIds(): string[] {
    if (typeof window === 'undefined') return [];
    const interviews = JSON.parse(localStorage.getItem(INTERVIEWS_KEY) || '{}');
    return Object.keys(interviews);
}
